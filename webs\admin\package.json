{"name": "nannan-kitchen-admin", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src --ext .vue,.js,.jsx --fix"}, "dependencies": {"@vueuse/core": "^11.2.0", "axios": "^1.7.7", "dayjs": "^1.11.13", "echarts": "^5.5.1", "element-plus": "^2.8.8", "js-cookie": "^3.0.5", "nprogress": "^0.2.0", "pinia": "^2.2.6", "vue": "^3.5.12", "vue-router": "^4.4.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.4", "@vitejs/plugin-vue-jsx": "^5.1.4", "autoprefixer": "^10.4.20", "eslint": "^9.14.0", "eslint-plugin-vue": "^9.29.1", "postcss": "^8.4.47", "prettier": "^3.3.3", "sass": "^1.80.6", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.27.4", "vite": "^5.4.10"}}