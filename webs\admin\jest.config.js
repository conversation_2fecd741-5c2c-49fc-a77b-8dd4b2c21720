export default {
  testEnvironment: "jsdom",
  transform: {
    "^.+\\.vue$": "@vue/vue3-jest",
    "^.+\\.(js|jsx)$": "babel-jest"
  },
  moduleFileExtensions: ["vue", "js", "json", "jsx"],
  moduleNameMapper: {
    "^@/(.*)$": "<rootDir>/src/$1"
  },
  testMatch: ["**/tests/**/*.spec.(js|jsx)", "**/tests/**/*.test.(js|jsx)"],
  collectCoverageFrom: [
    "src/**/*.{js,vue}",
    "!src/main.js",
    "!**/node_modules/**"
  ],
  setupFilesAfterEnv: ["<rootDir>/tests/setup.js"]
};
