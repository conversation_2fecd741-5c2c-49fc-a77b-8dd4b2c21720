#!/usr/bin/env node

/**
 * 诊断脚本 - 检查Vite服务器问题
 */

import http from "http";
import { fileURLToPath } from "url";
import { dirname, resolve } from "path";
import fs from "fs";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log("🔍 楠楠家厨后台管理系统 - 诊断工具");
console.log("=".repeat(50));

// 检查文件是否存在
function checkFiles() {
  console.log("\n📁 检查关键文件...");

  const files = [
    "index.html",
    "src/main-simple.js",
    "src/App.vue",
    "package.json",
    "vite.config.js"
  ];

  files.forEach(file => {
    const filePath = resolve(__dirname, file);
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${file}`);
    } else {
      console.log(`❌ ${file} - 文件不存在`);
    }
  });
}

// 测试HTTP请求
function testHttpRequest() {
  console.log("\n🌐 测试HTTP请求...");

  return new Promise((resolve, reject) => {
    const req = http.request(
      {
        hostname: "localhost",
        port: 3001,
        path: "/",
        method: "GET",
        timeout: 5000
      },
      res => {
        console.log(`✅ HTTP状态码: ${res.statusCode}`);
        console.log(`✅ 响应头:`, res.headers);

        let data = "";
        res.on("data", chunk => {
          data += chunk;
        });

        res.on("end", () => {
          console.log(`✅ 响应内容长度: ${data.length} 字节`);
          if (data.length > 0) {
            console.log(`✅ 响应内容预览: ${data.substring(0, 200)}...`);
          }
          resolve(data);
        });
      }
    );

    req.on("error", err => {
      console.log(`❌ HTTP请求失败: ${err.message}`);
      reject(err);
    });

    req.on("timeout", () => {
      console.log(`❌ HTTP请求超时`);
      req.destroy();
      reject(new Error("Request timeout"));
    });

    req.end();
  });
}

// 检查端口占用
function checkPort() {
  console.log("\n🔌 检查端口占用...");

  return new Promise(resolve => {
    const server = http.createServer();

    server.listen(5173, "localhost", () => {
      console.log("❌ 端口5173未被占用 - 这可能是问题所在！");
      server.close();
      resolve(false);
    });

    server.on("error", err => {
      if (err.code === "EADDRINUSE") {
        console.log("✅ 端口5173已被占用 - 正常");
        resolve(true);
      } else {
        console.log(`❌ 端口检查错误: ${err.message}`);
        resolve(false);
      }
    });
  });
}

// 主诊断函数
async function diagnose() {
  try {
    checkFiles();

    const portInUse = await checkPort();

    if (portInUse) {
      await testHttpRequest();
    } else {
      console.log("\n⚠️ Vite服务器可能没有正确启动");
      console.log("请尝试重新启动服务器：");
      console.log("npx vite --port 5173 --host 0.0.0.0");
    }

    console.log("\n🔧 建议的解决方案:");
    console.log("1. 清除浏览器缓存 (Ctrl+Shift+Delete)");
    console.log("2. 尝试无痕模式访问");
    console.log("3. 尝试不同的浏览器");
    console.log("4. 检查防火墙设置");
    console.log("5. 重启Vite服务器");
  } catch (error) {
    console.error("❌ 诊断过程中出现错误:", error.message);
  }
}

// 运行诊断
diagnose();
