// 全局样式文件
@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";
@import "animate.css";

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial,
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

#app {
  height: 100%;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;

  &:hover {
    background: #a8a8a8;
  }
}

// 布局样式
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.layout-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.layout-sidebar {
  width: 200px;
  background: #001529;
  transition: width 0.3s;

  &.collapsed {
    width: 64px;
  }
}

.layout-content {
  flex: 1;
  padding: 20px;
  background: #f0f2f5;
  overflow-y: auto;
}

// 卡片样式
.content-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

// 表格样式
.table-container {
  .el-table {
    border-radius: 8px;
    overflow: hidden;
  }

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .table-title {
      font-size: 16px;
      font-weight: 500;
      color: #262626;
    }
  }
}

// 表单样式
.form-container {
  .el-form-item {
    margin-bottom: 20px;
  }

  .form-actions {
    text-align: center;
    margin-top: 30px;

    .el-button {
      margin: 0 10px;
    }
  }
}

// 统计卡片样式
.stats-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 20px;

  .stats-title {
    font-size: 14px;
    opacity: 0.8;
    margin-bottom: 8px;
  }

  .stats-value {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 8px;
  }

  .stats-desc {
    font-size: 12px;
    opacity: 0.7;
  }

  &.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  &.success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  &.warning {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  }

  &.danger {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  }
}

// 工具类
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-20 {
  margin-top: 20px;
}

.flex {
  display: flex;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

// Element Plus 样式优化
.el-button {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}

.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
}

.el-table {
  border-radius: 8px;
  overflow: hidden;

  .el-table__header {
    background-color: #f8fafc;
  }

  .el-table__row:hover {
    background-color: #f1f5f9;
  }
}

.el-dialog {
  border-radius: 12px;

  .el-dialog__header {
    padding: 20px 20px 10px;
    border-bottom: 1px solid #ebeef5;
  }

  .el-dialog__body {
    padding: 20px;
  }
}

.el-form-item {
  margin-bottom: 20px;

  .el-form-item__label {
    font-weight: 500;
    color: #333;
  }
}

.el-input__wrapper {
  border-radius: 6px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &.is-focus {
    box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);
  }
}

.el-select {
  .el-input__wrapper {
    border-radius: 6px;
  }
}

.el-pagination {
  .el-pager li {
    border-radius: 4px;
    margin: 0 2px;
  }

  .btn-prev,
  .btn-next {
    border-radius: 4px;
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

// 响应式
@media (max-width: 768px) {
  .layout-sidebar {
    width: 64px;
  }

  .layout-content {
    padding: 10px;
  }

  .stats-card {
    padding: 16px;

    .stats-value {
      font-size: 24px;
    }
  }
}
