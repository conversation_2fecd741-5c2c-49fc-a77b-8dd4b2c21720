// 全局样式文件

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
}

// 布局样式
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.layout-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.layout-sidebar {
  width: 200px;
  background: #001529;
  transition: width 0.3s;
  
  &.collapsed {
    width: 64px;
  }
}

.layout-content {
  flex: 1;
  padding: 20px;
  background: #f0f2f5;
  overflow-y: auto;
}

// 卡片样式
.content-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

// 表格样式
.table-container {
  .el-table {
    border-radius: 8px;
    overflow: hidden;
  }
  
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .table-title {
      font-size: 16px;
      font-weight: 500;
      color: #262626;
    }
  }
}

// 表单样式
.form-container {
  .el-form-item {
    margin-bottom: 20px;
  }
  
  .form-actions {
    text-align: center;
    margin-top: 30px;
    
    .el-button {
      margin: 0 10px;
    }
  }
}

// 统计卡片样式
.stats-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 20px;
  
  .stats-title {
    font-size: 14px;
    opacity: 0.8;
    margin-bottom: 8px;
  }
  
  .stats-value {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 8px;
  }
  
  .stats-desc {
    font-size: 12px;
    opacity: 0.7;
  }
  
  &.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  &.success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }
  
  &.warning {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  }
  
  &.danger {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  }
}

// 工具类
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-20 {
  margin-top: 20px;
}

.flex {
  display: flex;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

// 响应式
@media (max-width: 768px) {
  .layout-sidebar {
    width: 64px;
  }
  
  .layout-content {
    padding: 10px;
  }
  
  .stats-card {
    padding: 16px;
    
    .stats-value {
      font-size: 24px;
    }
  }
}
