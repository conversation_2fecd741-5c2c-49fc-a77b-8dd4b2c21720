import { mount } from '@vue/test-utils'
import { describe, it, expect } from '@jest/globals'
import CustomTable from '@/components/CustomTable.vue'

describe('CustomTable', () => {
  const mockColumns = [
    { prop: 'name', label: '姓名' },
    { prop: 'age', label: '年龄' },
    { prop: 'email', label: '邮箱' }
  ]

  const mockData = [
    { id: 1, name: '张三', age: 25, email: '<EMAIL>' },
    { id: 2, name: '李四', age: 30, email: '<EMAIL>' }
  ]

  it('renders table with data', () => {
    const wrapper = mount(CustomTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        title: '用户列表'
      }
    })

    expect(wrapper.find('.table-title').text()).toBe('用户列表')
    expect(wrapper.findAll('tbody tr')).toHaveLength(2)
  })

  it('shows search form when showSearch is true', () => {
    const searchFields = [
      { prop: 'name', label: '姓名', type: 'input', placeholder: '请输入姓名' }
    ]

    const wrapper = mount(CustomTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        showSearch: true,
        searchFields
      }
    })

    expect(wrapper.find('.table-search').exists()).toBe(true)
    expect(wrapper.find('input[placeholder="请输入姓名"]').exists()).toBe(true)
  })

  it('emits search event when search button is clicked', async () => {
    const searchFields = [
      { prop: 'name', label: '姓名', type: 'input', placeholder: '请输入姓名' }
    ]

    const wrapper = mount(CustomTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        showSearch: true,
        searchFields
      }
    })

    const searchInput = wrapper.find('input[placeholder="请输入姓名"]')
    await searchInput.setValue('张三')

    const searchButton = wrapper.find('button[type="button"]')
    await searchButton.trigger('click')

    expect(wrapper.emitted('search')).toBeTruthy()
    expect(wrapper.emitted('search')[0][0]).toEqual({ name: '张三' })
  })

  it('shows pagination when showPagination is true', () => {
    const wrapper = mount(CustomTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        showPagination: true,
        pagination: { page: 1, size: 10, total: 20 }
      }
    })

    expect(wrapper.find('.table-pagination').exists()).toBe(true)
  })

  it('hides actions column when showActions is false', () => {
    const wrapper = mount(CustomTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        showActions: false
      }
    })

    expect(wrapper.find('th:last-child').text()).not.toBe('操作')
  })
})
