<template>
  <div class="user-list">
    <CustomTable
      title="用户管理"
      :data="tableData"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      :show-search="true"
      :search-fields="searchFields"
      @search="handleSearch"
      @reset="handleReset"
      @current-change="handleCurrentChange"
    >
      <template #role="{ row }">
        <el-tag :type="row.role === 'admin' ? 'danger' : 'primary'">
          {{ row.role === 'admin' ? '管理员' : '家庭成员' }}
        </el-tag>
      </template>
      
      <template #actions="{ row }">
        <el-button size="small" @click="handleView(row)">查看</el-button>
        <el-button size="small" type="primary" @click="handleEdit(row)">编辑</el-button>
        <el-button size="small" type="warning" @click="handleResetPassword(row)">重置密码</el-button>
      </template>
    </CustomTable>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import CustomTable from '@/components/CustomTable.vue'
import { userApi } from '@/api/user'
import dayjs from 'dayjs'

export default defineComponent({
  name: 'UserList',
  components: {
    CustomTable
  },
  setup() {
    const loading = ref(false)
    const tableData = ref([])
    
    const pagination = reactive({
      page: 1,
      size: 10,
      total: 0
    })
    
    const searchParams = reactive({
      name: '',
      phone: '',
      role: ''
    })
    
    const columns = [
      { prop: 'id', label: 'ID', width: 80 },
      { prop: 'name', label: '姓名' },
      { prop: 'phone', label: '手机号' },
      { prop: 'role', label: '角色', slot: 'role' },
      { prop: 'createdAt', label: '注册时间', formatter: (row) => dayjs(row.createdAt).format('YYYY-MM-DD') }
    ]
    
    const searchFields = [
      {
        prop: 'name',
        label: '姓名',
        type: 'input',
        placeholder: '请输入姓名'
      },
      {
        prop: 'phone',
        label: '手机号',
        type: 'input',
        placeholder: '请输入手机号'
      },
      {
        prop: 'role',
        label: '角色',
        type: 'select',
        placeholder: '选择角色',
        options: [
          { label: '管理员', value: 'admin' },
          { label: '家庭成员', value: 'member' }
        ]
      }
    ]
    
    const loadData = async () => {
      loading.value = true
      try {
        const params = {
          page: pagination.page,
          size: pagination.size,
          ...searchParams
        }
        
        const response = await userApi.getUsers(params)
        if (response.data) {
          tableData.value = response.data.list || response.data
          pagination.total = response.data.total || response.data.length
        }
      } catch (error) {
        console.error('加载用户列表失败:', error)
        ElMessage.error('加载数据失败')
      } finally {
        loading.value = false
      }
    }
    
    const handleSearch = (params) => {
      Object.assign(searchParams, params)
      pagination.page = 1
      loadData()
    }
    
    const handleReset = () => {
      Object.keys(searchParams).forEach(key => {
        searchParams[key] = ''
      })
      pagination.page = 1
      loadData()
    }
    
    const handleCurrentChange = (page) => {
      pagination.page = page
      loadData()
    }
    
    const handleView = (row) => {
      ElMessage.info(`查看用户: ${row.name}`)
    }
    
    const handleEdit = (row) => {
      ElMessage.info(`编辑用户: ${row.name}`)
    }
    
    const handleResetPassword = async (row) => {
      try {
        await userApi.resetPassword(row.id, '123456')
        ElMessage.success('密码重置成功，新密码为：123456')
      } catch (error) {
        console.error('重置密码失败:', error)
        ElMessage.error('重置密码失败')
      }
    }
    
    onMounted(() => {
      loadData()
    })
    
    return {
      loading,
      tableData,
      columns,
      searchFields,
      pagination,
      handleSearch,
      handleReset,
      handleCurrentChange,
      handleView,
      handleEdit,
      handleResetPassword
    }
  }
})
</script>
