<template>
  <div class="layout-container">
    <!-- 头部 -->
    <div class="layout-header">
      <div class="header-left">
        <el-button type="text" @click="toggleSidebar" class="sidebar-toggle">
          <el-icon><Menu /></el-icon>
        </el-button>
        <h1 class="logo">楠楠家厨管理系统</h1>
      </div>

      <div class="header-right">
        <el-dropdown @command="handleCommand">
          <span class="user-info">
            <el-avatar :size="32" :src="userInfo.avatar" />
            <span class="username">{{ userInfo.name }}</span>
            <el-icon><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人信息</el-dropdown-item>
              <el-dropdown-item command="logout" divided
                >退出登录</el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 主体 -->
    <div class="layout-main">
      <!-- 侧边栏 -->
      <div :class="['layout-sidebar', { collapsed: isCollapsed }]">
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapsed"
          :unique-opened="true"
          background-color="#001529"
          text-color="#fff"
          active-text-color="#1890ff"
          router
        >
          <template v-for="route in menuRoutes" :key="route.path">
            <el-sub-menu
              v-if="route.children && route.children.length > 1"
              :index="route.path"
            >
              <template #title>
                <el-icon><component :is="getIcon(route.meta?.icon)" /></el-icon>
                <span>{{ route.meta?.title }}</span>
              </template>
              <el-menu-item
                v-for="child in route.children"
                :key="child.path"
                :index="child.path"
              >
                <el-icon><component :is="getIcon(child.meta?.icon)" /></el-icon>
                <span>{{ child.meta?.title }}</span>
              </el-menu-item>
            </el-sub-menu>

            <el-menu-item
              v-else
              :index="route.children?.[0]?.path || route.path"
            >
              <el-icon
                ><component
                  :is="
                    getIcon(route.meta?.icon || route.children?.[0]?.meta?.icon)
                  "
              /></el-icon>
              <span>{{
                route.meta?.title || route.children?.[0]?.meta?.title
              }}</span>
            </el-menu-item>
          </template>
        </el-menu>
      </div>

      <!-- 内容区域 -->
      <div class="layout-content">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useUserStore } from "@/stores/user";
import {
  Menu,
  ArrowDown,
  House,
  List,
  ShoppingCart,
  ChatDotRound,
  User,
  PieChart,
  Bowl,
  Grid,
  Bell,
  Calendar
} from "@element-plus/icons-vue";

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

const isCollapsed = ref(false);

// 图标映射
const iconMap = {
  dashboard: House,
  menu: Bowl,
  list: List,
  dish: Bowl,
  category: Grid,
  order: ShoppingCart,
  today: Calendar,
  message: ChatDotRound,
  notification: Bell,
  user: User,
  chart: PieChart,
  overview: PieChart
};

// 获取菜单路由
const menuRoutes = computed(() => {
  return router.options.routes
    .filter(
      route => route.path !== "/login" && route.component?.name !== "Layout"
    )
    .filter(route => route.path !== "/");
});

// 当前激活的菜单
const activeMenu = computed(() => {
  return route.path;
});

// 用户信息
const userInfo = computed(() => {
  return (
    userStore.userInfo || {
      name: "管理员",
      avatar:
        "https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
    }
  );
});

// 切换侧边栏
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value;
};

// 获取图标组件
const getIcon = iconName => {
  return iconMap[iconName] || List;
};

// 处理下拉菜单命令
const handleCommand = command => {
  if (command === "logout") {
    userStore.logout();
    router.push("/login");
  } else if (command === "profile") {
    // 跳转到个人信息页面
    console.log("跳转到个人信息页面");
  }
};
</script>

<style scoped lang="scss">
.layout-container {
  @apply h-screen flex flex-col;
}

.layout-header {
  @apply h-16 bg-white border-b border-gray-200 flex items-center justify-between px-4 shadow-sm;
}

.header-left {
  @apply flex items-center gap-4;
}

.sidebar-toggle {
  @apply text-gray-600 hover:text-gray-800 transition-colors;

  :deep(.el-button) {
    @apply border-none bg-transparent;
  }
}

.logo {
  @apply text-xl font-bold text-blue-600 m-0;
}

.header-right {
  @apply flex items-center;
}

.user-info {
  @apply flex items-center gap-2 cursor-pointer hover:bg-gray-50 px-3 py-2 rounded-md transition-colors;
}

.username {
  @apply text-gray-700 font-medium text-sm;
}

.layout-main {
  @apply flex-1 flex overflow-hidden;
}

.layout-sidebar {
  @apply w-64 bg-gray-900 transition-all duration-300;

  &.collapsed {
    @apply w-16;
  }

  :deep(.el-menu) {
    border-right: none;
    height: 100%;
  }

  :deep(.el-menu-item) {
    @apply transition-all duration-200;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1) !important;
    }

    &.is-active {
      background-color: #1890ff !important;
    }
  }

  :deep(.el-sub-menu__title) {
    @apply transition-all duration-200;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1) !important;
    }
  }
}

.layout-content {
  @apply flex-1 bg-gray-50 overflow-auto p-6;
}

// 响应式设计
@media (max-width: 768px) {
  .layout-sidebar {
    @apply fixed left-0 top-16 h-full z-50 transform transition-transform;

    &.collapsed {
      @apply -translate-x-full w-64;
    }
  }

  .layout-content {
    @apply p-4;
  }

  .header-left {
    @apply gap-2;
  }

  .logo {
    @apply text-lg;
  }

  .username {
    @apply hidden;
  }
}

// 暗色模式支持
@media (prefers-color-scheme: dark) {
  .layout-header {
    @apply bg-gray-800 border-gray-700;
  }

  .logo {
    @apply text-blue-400;
  }

  .sidebar-toggle {
    @apply text-gray-300 hover:text-white;
  }

  .user-info {
    @apply hover:bg-gray-700;
  }

  .username {
    @apply text-gray-200;
  }

  .layout-content {
    @apply bg-gray-900;
  }
}
</style>
