<template>
  <div class="layout-container">
    <!-- 头部 -->
    <div class="layout-header">
      <div class="header-left">
        <el-button
          type="text"
          @click="toggleSidebar"
          class="sidebar-toggle"
        >
          <el-icon><Menu /></el-icon>
        </el-button>
        <h1 class="logo">楠楠家厨管理系统</h1>
      </div>
      
      <div class="header-right">
        <el-dropdown @command="handleCommand">
          <span class="user-info">
            <el-avatar :size="32" :src="userInfo.avatar" />
            <span class="username">{{ userInfo.name }}</span>
            <el-icon><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人信息</el-dropdown-item>
              <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    
    <!-- 主体 -->
    <div class="layout-main">
      <!-- 侧边栏 -->
      <div :class="['layout-sidebar', { collapsed: isCollapsed }]">
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapsed"
          :unique-opened="true"
          background-color="#001529"
          text-color="#fff"
          active-text-color="#1890ff"
          router
        >
          <template v-for="route in menuRoutes" :key="route.path">
            <el-sub-menu v-if="route.children && route.children.length > 1" :index="route.path">
              <template #title>
                <el-icon><component :is="getIcon(route.meta?.icon)" /></el-icon>
                <span>{{ route.meta?.title }}</span>
              </template>
              <el-menu-item
                v-for="child in route.children"
                :key="child.path"
                :index="child.path"
              >
                <el-icon><component :is="getIcon(child.meta?.icon)" /></el-icon>
                <span>{{ child.meta?.title }}</span>
              </el-menu-item>
            </el-sub-menu>
            
            <el-menu-item v-else :index="route.children?.[0]?.path || route.path">
              <el-icon><component :is="getIcon(route.meta?.icon || route.children?.[0]?.meta?.icon)" /></el-icon>
              <span>{{ route.meta?.title || route.children?.[0]?.meta?.title }}</span>
            </el-menu-item>
          </template>
        </el-menu>
      </div>
      
      <!-- 内容区域 -->
      <div class="layout-content">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import {
  Menu,
  ArrowDown,
  House,
  List,
  ShoppingCart,
  ChatDotRound,
  User,
  PieChart,
  Bowl,
  Grid,
  Bell,
  Calendar
} from '@element-plus/icons-vue'

export default defineComponent({
  name: 'Layout',
  components: {
    Menu,
    ArrowDown,
    House,
    List,
    ShoppingCart,
    ChatDotRound,
    User,
    PieChart,
    Bowl,
    Grid,
    Bell,
    Calendar
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const userStore = useUserStore()
    
    const isCollapsed = ref(false)
    
    // 获取菜单路由
    const menuRoutes = computed(() => {
      return router.options.routes.filter(route => 
        route.path !== '/login' && route.component?.name !== 'Layout'
      ).filter(route => route.path !== '/')
    })
    
    // 当前激活的菜单
    const activeMenu = computed(() => {
      return route.path
    })
    
    // 用户信息
    const userInfo = computed(() => {
      return userStore.userInfo || {
        name: '管理员',
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
      }
    })
    
    // 切换侧边栏
    const toggleSidebar = () => {
      isCollapsed.value = !isCollapsed.value
    }
    
    // 获取图标组件
    const getIcon = (iconName) => {
      const iconMap = {
        dashboard: House,
        menu: Bowl,
        list: List,
        dish: Bowl,
        category: Grid,
        order: ShoppingCart,
        today: Calendar,
        message: ChatDotRound,
        notification: Bell,
        user: User,
        chart: PieChart,
        overview: PieChart
      }
      return iconMap[iconName] || List
    }
    
    // 处理下拉菜单命令
    const handleCommand = (command) => {
      if (command === 'logout') {
        userStore.logout()
        router.push('/login')
      } else if (command === 'profile') {
        // 跳转到个人信息页面
        console.log('跳转到个人信息页面')
      }
    }
    
    return {
      isCollapsed,
      menuRoutes,
      activeMenu,
      userInfo,
      toggleSidebar,
      getIcon,
      handleCommand
    }
  }
})
</script>

<style scoped>
.header-left {
  display: flex;
  align-items: center;
}

.sidebar-toggle {
  margin-right: 16px;
  font-size: 18px;
}

.logo {
  font-size: 20px;
  font-weight: bold;
  color: #1890ff;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f5f5;
}

.username {
  margin: 0 8px;
  font-size: 14px;
}

.layout-sidebar .el-menu {
  border-right: none;
  height: 100%;
}

.layout-content {
  background: #f0f2f5;
}
</style>
