<template>
  <div class="dish-management">
    <CustomTable
      title="菜品管理"
      :data="tableData"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      :show-search="true"
      :search-fields="searchFields"
      @search="handleSearch"
      @reset="handleReset"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    >
      <template #actions>
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>
          新增菜品
        </el-button>
      </template>
      
      <template #image="{ row }">
        <el-image
          :src="row.image || '/default-dish.jpg'"
          :alt="row.name"
          style="width: 60px; height: 60px; border-radius: 6px;"
          fit="cover"
          :preview-src-list="[row.image]"
        />
      </template>
      
      <template #category="{ row }">
        <el-tag>{{ row.category }}</el-tag>
      </template>
      
      <template #isAvailable="{ row }">
        <el-switch
          v-model="row.isAvailable"
          @change="handleStatusChange(row)"
        />
      </template>
      
      <template #actions="{ row }">
        <el-button size="small" @click="handleView(row)">查看</el-button>
        <el-button size="small" type="primary" @click="handleEdit(row)">编辑</el-button>
        <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
      </template>
    </CustomTable>
    
    <!-- 菜品表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <DishForm
        ref="dishFormRef"
        :form-data="formData"
        :is-edit="isEdit"
        @submit="handleSubmit"
      />
    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import CustomTable from '@/components/CustomTable.vue'
import DishForm from './components/DishForm.vue'
import { dishApi } from '@/api/menu'

export default defineComponent({
  name: 'DishManagement',
  components: {
    CustomTable,
    DishForm,
    Plus
  },
  setup() {
    const loading = ref(false)
    const tableData = ref([])
    const dialogVisible = ref(false)
    const isEdit = ref(false)
    const dishFormRef = ref()
    
    const pagination = reactive({
      page: 1,
      size: 10,
      total: 0
    })
    
    const searchParams = reactive({
      name: '',
      category: '',
      isAvailable: ''
    })
    
    const formData = ref({})
    
    // 表格列配置
    const columns = [
      { prop: 'id', label: 'ID', width: 80 },
      { prop: 'image', label: '图片', slot: 'image', width: 80 },
      { prop: 'name', label: '菜品名称', minWidth: 120 },
      { prop: 'category', label: '分类', slot: 'category', width: 100 },
      { prop: 'description', label: '描述', showOverflowTooltip: true, minWidth: 150 },
      { prop: 'ingredients', label: '食材', showOverflowTooltip: true, minWidth: 120 },
      { prop: 'cookingMethod', label: '制作方法', showOverflowTooltip: true, minWidth: 120 },
      { prop: 'isAvailable', label: '状态', slot: 'isAvailable', width: 80 }
    ]
    
    // 搜索字段配置
    const searchFields = [
      {
        prop: 'name',
        label: '菜品名称',
        type: 'input',
        placeholder: '请输入菜品名称'
      },
      {
        prop: 'category',
        label: '分类',
        type: 'select',
        placeholder: '选择分类',
        options: [
          { label: '热菜', value: '热菜' },
          { label: '凉菜', value: '凉菜' },
          { label: '汤品', value: '汤品' },
          { label: '主食', value: '主食' },
          { label: '甜品', value: '甜品' }
        ]
      },
      {
        prop: 'isAvailable',
        label: '状态',
        type: 'select',
        placeholder: '选择状态',
        options: [
          { label: '可用', value: true },
          { label: '不可用', value: false }
        ]
      }
    ]
    
    // 对话框标题
    const dialogTitle = computed(() => {
      return isEdit.value ? '编辑菜品' : '新增菜品'
    })
    
    // 加载数据
    const loadData = async () => {
      loading.value = true
      try {
        const params = {
          page: pagination.page,
          size: pagination.size,
          ...searchParams
        }
        
        const response = await dishApi.getDishes(params)
        if (response.data) {
          tableData.value = response.data.list || response.data
          pagination.total = response.data.total || response.data.length
        }
      } catch (error) {
        console.error('加载菜品列表失败:', error)
        ElMessage.error('加载数据失败')
      } finally {
        loading.value = false
      }
    }
    
    // 搜索
    const handleSearch = (params) => {
      Object.assign(searchParams, params)
      pagination.page = 1
      loadData()
    }
    
    // 重置
    const handleReset = () => {
      Object.keys(searchParams).forEach(key => {
        searchParams[key] = ''
      })
      pagination.page = 1
      loadData()
    }
    
    // 分页变化
    const handleCurrentChange = (page) => {
      pagination.page = page
      loadData()
    }
    
    const handleSizeChange = (size) => {
      pagination.size = size
      pagination.page = 1
      loadData()
    }
    
    // 新增
    const handleCreate = () => {
      isEdit.value = false
      formData.value = {}
      dialogVisible.value = true
    }
    
    // 查看
    const handleView = (row) => {
      console.log('查看菜品:', row)
    }
    
    // 编辑
    const handleEdit = (row) => {
      isEdit.value = true
      formData.value = { ...row }
      dialogVisible.value = true
    }
    
    // 删除
    const handleDelete = async (row) => {
      try {
        await ElMessageBox.confirm('确定要删除这个菜品吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        await dishApi.deleteDish(row.id)
        ElMessage.success('删除成功')
        loadData()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除菜品失败:', error)
          ElMessage.error('删除失败')
        }
      }
    }
    
    // 状态变化
    const handleStatusChange = async (row) => {
      try {
        await dishApi.updateDish(row.id, { isAvailable: row.isAvailable })
        ElMessage.success('状态更新成功')
      } catch (error) {
        console.error('更新状态失败:', error)
        ElMessage.error('状态更新失败')
        // 恢复原状态
        row.isAvailable = !row.isAvailable
      }
    }
    
    // 提交表单
    const handleSubmit = async (data) => {
      try {
        if (isEdit.value) {
          await dishApi.updateDish(formData.value.id, data)
          ElMessage.success('更新成功')
        } else {
          await dishApi.createDish(data)
          ElMessage.success('创建成功')
        }
        
        dialogVisible.value = false
        loadData()
      } catch (error) {
        console.error('保存菜品失败:', error)
        ElMessage.error('保存失败')
      }
    }
    
    // 关闭对话框
    const handleDialogClose = () => {
      formData.value = {}
    }
    
    onMounted(() => {
      loadData()
    })
    
    return {
      loading,
      tableData,
      columns,
      searchFields,
      pagination,
      dialogVisible,
      dialogTitle,
      isEdit,
      formData,
      dishFormRef,
      handleSearch,
      handleReset,
      handleCurrentChange,
      handleSizeChange,
      handleCreate,
      handleView,
      handleEdit,
      handleDelete,
      handleStatusChange,
      handleSubmit,
      handleDialogClose
    }
  }
})
</script>
