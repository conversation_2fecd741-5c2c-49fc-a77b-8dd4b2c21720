#!/usr/bin/env node

/**
 * 开发服务器启动脚本
 */

import { createServer } from 'vite'
import { resolve } from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = resolve(__filename, '..')

async function startDevServer() {
  try {
    console.log('🚀 启动楠楠家厨后台管理系统开发服务器...')
    
    const server = await createServer({
      root: __dirname,
      server: {
        port: 5173,
        host: '0.0.0.0',
        open: true
      }
    })
    
    await server.listen()
    
    console.log('✅ 开发服务器启动成功！')
    console.log('📱 访问地址: http://localhost:5173')
    console.log('🔑 测试账号: 13800138000 / 123456')
    
  } catch (error) {
    console.error('❌ 启动失败:', error)
    process.exit(1)
  }
}

startDevServer()
