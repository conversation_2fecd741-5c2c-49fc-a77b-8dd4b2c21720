<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb-20">
      <el-col :xs="24" :sm="12" :md="6" v-for="(stat, index) in statistics" :key="index">
        <div :class="['stats-card', stat.type]">
          <div class="stats-title">{{ stat.title }}</div>
          <div class="stats-value">{{ stat.value }}</div>
          <div class="stats-desc">{{ stat.desc }}</div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 图表区域 -->
    <el-row :gutter="20" class="mb-20">
      <el-col :xs="24" :md="12">
        <div class="content-card">
          <h3>订单趋势</h3>
          <div ref="orderChartRef" style="height: 300px;"></div>
        </div>
      </el-col>
      <el-col :xs="24" :md="12">
        <div class="content-card">
          <h3>菜品分类统计</h3>
          <div ref="categoryChartRef" style="height: 300px;"></div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 最新数据 -->
    <el-row :gutter="20">
      <el-col :xs="24" :md="12">
        <div class="content-card">
          <h3>最新订单</h3>
          <el-table :data="recentOrders" style="width: 100%">
            <el-table-column prop="id" label="订单号" width="80" />
            <el-table-column prop="userName" label="用户" />
            <el-table-column prop="items" label="菜品" />
            <el-table-column prop="status" label="状态">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
      <el-col :xs="24" :md="12">
        <div class="content-card">
          <h3>最新留言</h3>
          <div class="message-list">
            <div v-for="message in recentMessages" :key="message.id" class="message-item">
              <div class="message-user">{{ message.userName }}</div>
              <div class="message-content">{{ message.content }}</div>
              <div class="message-time">{{ formatTime(message.createdAt) }}</div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { menuApi } from '@/api/menu'
import { orderApi } from '@/api/order'
import { messageApi } from '@/api/message'
import dayjs from 'dayjs'

export default defineComponent({
  name: 'Dashboard',
  setup() {
    const orderChartRef = ref()
    const categoryChartRef = ref()
    
    const statistics = ref([
      { title: '今日菜品', value: 0, desc: '比昨日 +0', type: 'primary' },
      { title: '今日订单', value: 0, desc: '比昨日 +0', type: 'success' },
      { title: '本周最爱', value: '暂无', desc: '用户最喜欢', type: 'warning' },
      { title: '月访问量', value: 0, desc: '总访问次数', type: 'danger' }
    ])
    
    const recentOrders = ref([])
    const recentMessages = ref([])
    
    // 加载统计数据
    const loadStatistics = async () => {
      try {
        const stats = await menuApi.getStatistics()
        if (stats.data) {
          statistics.value[0].value = stats.data.todayDishes
          statistics.value[1].value = stats.data.totalOrders
          statistics.value[2].value = stats.data.weeklyFavorite
          statistics.value[3].value = stats.data.monthlyVisits
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    }
    
    // 加载最新订单
    const loadRecentOrders = async () => {
      try {
        const orders = await orderApi.getTodayOrders()
        if (orders.data) {
          recentOrders.value = orders.data.slice(0, 5).map(order => ({
            id: order.id,
            userName: order.user?.name || '未知用户',
            items: JSON.parse(order.items || '[]').map(item => item.dishName).join(', '),
            status: order.status
          }))
        }
      } catch (error) {
        console.error('加载最新订单失败:', error)
      }
    }
    
    // 加载最新留言
    const loadRecentMessages = async () => {
      try {
        const messages = await messageApi.getMessages({ limit: 5 })
        if (messages.data) {
          recentMessages.value = messages.data.slice(0, 5)
        }
      } catch (error) {
        console.error('加载最新留言失败:', error)
      }
    }
    
    // 初始化订单趋势图表
    const initOrderChart = () => {
      const chart = echarts.init(orderChartRef.value)
      const option = {
        title: {
          text: '近7天订单趋势'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          data: [12, 19, 3, 5, 2, 3, 8],
          type: 'line',
          smooth: true,
          itemStyle: {
            color: '#1890ff'
          }
        }]
      }
      chart.setOption(option)
    }
    
    // 初始化分类统计图表
    const initCategoryChart = () => {
      const chart = echarts.init(categoryChartRef.value)
      const option = {
        title: {
          text: '菜品分类分布'
        },
        tooltip: {
          trigger: 'item'
        },
        series: [{
          type: 'pie',
          radius: '50%',
          data: [
            { value: 35, name: '热菜' },
            { value: 25, name: '凉菜' },
            { value: 20, name: '汤品' },
            { value: 15, name: '主食' },
            { value: 5, name: '甜品' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }
      chart.setOption(option)
    }
    
    // 获取状态类型
    const getStatusType = (status) => {
      const statusMap = {
        pending: 'warning',
        completed: 'success',
        cancelled: 'danger'
      }
      return statusMap[status] || 'info'
    }
    
    // 获取状态文本
    const getStatusText = (status) => {
      const statusMap = {
        pending: '待处理',
        completed: '已完成',
        cancelled: '已取消'
      }
      return statusMap[status] || '未知'
    }
    
    // 格式化时间
    const formatTime = (time) => {
      return dayjs(time).format('MM-DD HH:mm')
    }
    
    onMounted(async () => {
      await loadStatistics()
      await loadRecentOrders()
      await loadRecentMessages()
      
      nextTick(() => {
        initOrderChart()
        initCategoryChart()
      })
    })
    
    return {
      orderChartRef,
      categoryChartRef,
      statistics,
      recentOrders,
      recentMessages,
      getStatusType,
      getStatusText,
      formatTime
    }
  }
})
</script>

<style scoped>
.message-list {
  max-height: 300px;
  overflow-y: auto;
}

.message-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.message-item:last-child {
  border-bottom: none;
}

.message-user {
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.message-content {
  color: #666;
  margin-bottom: 4px;
}

.message-time {
  font-size: 12px;
  color: #999;
}
</style>
