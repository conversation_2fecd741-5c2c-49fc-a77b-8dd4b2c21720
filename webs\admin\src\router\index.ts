import { createRouter, createWebHistory } from "vue-router";
import Layout from "@/layout/index.vue";

const routes = [
  {
    path: "/login",
    name: "<PERSON><PERSON>",
    component: () => import("@/views/login/index.vue"),
    meta: { title: "登录" }
  },
  {
    path: "/",
    component: Layout,
    redirect: "/dashboard",
    children: [
      {
        path: "dashboard",
        name: "Dashboard",
        component: () => import("@/views/dashboard/index.vue"),
        meta: { title: "仪表盘", icon: "dashboard" }
      }
    ]
  },
  {
    path: "/menu",
    component: Layout,
    redirect: "/menu/dishes",
    meta: { title: "菜单管理", icon: "menu" },
    children: [
      {
        path: "dishes",
        name: "DishList",
        component: () => import("@/views/menu/dishes.vue"),
        meta: { title: "菜品管理", icon: "dish" }
      }
    ]
  },
  {
    path: "/order",
    component: Layout,
    redirect: "/order/list",
    meta: { title: "订单管理", icon: "order" },
    children: [
      {
        path: "list",
        name: "OrderList",
        component: () => import("@/views/order/list.vue"),
        meta: { title: "订单列表", icon: "list" }
      }
    ]
  },
  {
    path: "/user",
    component: Layout,
    redirect: "/user/list",
    meta: { title: "用户管理", icon: "user" },
    children: [
      {
        path: "list",
        name: "UserList",
        component: () => import("@/views/user/list.vue"),
        meta: { title: "用户列表", icon: "list" }
      }
    ]
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 楠楠家厨管理系统`;
  }

  // 检查登录状态
  const token = localStorage.getItem("admin_token");

  if (to.path === "/login") {
    next();
  } else if (!token) {
    next("/login");
  } else {
    next();
  }
});

export default router;
