import axios from "axios";
import { ElMessage } from "element-plus";
import { useUserStore } from "@/stores/user";

// 创建axios实例
const request = axios.create({
  baseURL: "/api",
  timeout: 10000,
  headers: {
    "Content-Type": "application/json"
  }
});

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 添加token
    const userStore = useUserStore();
    if (userStore.token) {
      config.headers.Authorization = `Bearer ${userStore.token}`;
    }

    return config;
  },
  error => {
    console.error("请求错误:", error);
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  response => {
    const { data } = response;

    // 如果是文件下载，直接返回
    if (response.config.responseType === "blob") {
      return response;
    }

    // 统一处理响应
    if (data.code === 200) {
      return data;
    } else {
      ElMessage.error(data.message || "请求失败");
      return Promise.reject(new Error(data.message || "请求失败"));
    }
  },
  error => {
    console.error("响应错误:", error);

    // 处理不同的错误状态码
    if (error.response) {
      const { status, data } = error.response;

      switch (status) {
        case 401:
          ElMessage.error("登录已过期，请重新登录");
          const userStore = useUserStore();
          userStore.logout();
          window.location.href = "/login";
          break;
        case 403:
          ElMessage.error("没有权限访问");
          break;
        case 404:
          ElMessage.error("请求的资源不存在");
          break;
        case 500:
          ElMessage.error("服务器内部错误");
          break;
        default:
          ElMessage.error(data?.message || "请求失败");
      }
    } else if (error.request) {
      ElMessage.error("网络错误，请检查网络连接");
    } else {
      ElMessage.error("请求配置错误");
    }

    return Promise.reject(error);
  }
);

// 封装常用的请求方法
export const http = {
  get(url, params = {}) {
    return request.get(url, { params });
  },

  post(url, data = {}) {
    return request.post(url, data);
  },

  put(url, data = {}) {
    return request.put(url, data);
  },

  delete(url, params = {}) {
    return request.delete(url, { params });
  },

  upload(url, formData) {
    return request.post(url, formData, {
      headers: {
        "Content-Type": "multipart/form-data"
      }
    });
  },

  download(url, params = {}) {
    return request.get(url, {
      params,
      responseType: "blob"
    });
  }
};

export default request;
