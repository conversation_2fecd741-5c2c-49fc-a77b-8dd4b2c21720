<template>
  <div class="category-management">
    <CustomTable
      title="分类管理"
      :data="tableData"
      :columns="columns"
      :loading="loading"
      :show-pagination="false"
    >
      <template #actions>
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>
          新增分类
        </el-button>
      </template>
      
      <template #actions="{ row }">
        <el-button size="small" type="primary" @click="handleEdit(row)">编辑</el-button>
        <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
      </template>
    </CustomTable>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import CustomTable from '@/components/CustomTable.vue'

export default defineComponent({
  name: 'CategoryManagement',
  components: {
    CustomTable,
    Plus
  },
  setup() {
    const loading = ref(false)
    const tableData = ref([
      { id: 1, name: '热菜', description: '热菜类别' },
      { id: 2, name: '凉菜', description: '凉菜类别' },
      { id: 3, name: '汤品', description: '汤品类别' },
      { id: 4, name: '主食', description: '主食类别' },
      { id: 5, name: '甜品', description: '甜品类别' }
    ])
    
    const columns = [
      { prop: 'id', label: 'ID', width: 80 },
      { prop: 'name', label: '分类名称' },
      { prop: 'description', label: '描述' }
    ]
    
    const handleCreate = () => {
      ElMessage.info('新增分类功能开发中...')
    }
    
    const handleEdit = (row) => {
      ElMessage.info(`编辑分类: ${row.name}`)
    }
    
    const handleDelete = (row) => {
      ElMessage.info(`删除分类: ${row.name}`)
    }
    
    return {
      loading,
      tableData,
      columns,
      handleCreate,
      handleEdit,
      handleDelete
    }
  }
})
</script>
