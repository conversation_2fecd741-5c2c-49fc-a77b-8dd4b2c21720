<template>
  <div class="table-container">
    <!-- 表格头部 -->
    <div class="table-header" v-if="showHeader">
      <div class="table-title">{{ title }}</div>
      <div class="table-actions">
        <slot name="actions"></slot>
      </div>
    </div>
    
    <!-- 搜索栏 -->
    <div class="table-search" v-if="showSearch">
      <el-form :model="searchForm" inline>
        <el-form-item
          v-for="field in searchFields"
          :key="field.prop"
          :label="field.label"
        >
          <el-input
            v-if="field.type === 'input'"
            v-model="searchForm[field.prop]"
            :placeholder="field.placeholder"
            clearable
          />
          <el-select
            v-else-if="field.type === 'select'"
            v-model="searchForm[field.prop]"
            :placeholder="field.placeholder"
            clearable
          >
            <el-option
              v-for="option in field.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
          <el-date-picker
            v-else-if="field.type === 'date'"
            v-model="searchForm[field.prop]"
            type="date"
            :placeholder="field.placeholder"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 表格 -->
    <el-table
      :data="tableData"
      :loading="loading"
      v-bind="$attrs"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
    >
      <el-table-column
        v-if="showSelection"
        type="selection"
        width="55"
      />
      
      <el-table-column
        v-for="column in columns"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        :min-width="column.minWidth"
        :sortable="column.sortable"
        :formatter="column.formatter"
        :show-overflow-tooltip="column.showOverflowTooltip !== false"
      >
        <template #default="{ row, column: col, $index }" v-if="column.slot">
          <slot :name="column.slot" :row="row" :column="col" :index="$index"></slot>
        </template>
      </el-table-column>
      
      <el-table-column
        v-if="showActions"
        label="操作"
        :width="actionWidth"
        fixed="right"
      >
        <template #default="{ row, $index }">
          <slot name="actions" :row="row" :index="$index">
            <el-button
              v-for="action in actions"
              :key="action.name"
              :type="action.type || 'primary'"
              :size="action.size || 'small'"
              :disabled="action.disabled && action.disabled(row)"
              @click="action.handler(row, $index)"
            >
              {{ action.label }}
            </el-button>
          </slot>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="table-pagination" v-if="showPagination">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="pageSizes"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, watch } from 'vue'

export default defineComponent({
  name: 'CustomTable',
  props: {
    // 表格标题
    title: {
      type: String,
      default: ''
    },
    // 表格数据
    data: {
      type: Array,
      default: () => []
    },
    // 表格列配置
    columns: {
      type: Array,
      required: true
    },
    // 是否显示头部
    showHeader: {
      type: Boolean,
      default: true
    },
    // 是否显示搜索
    showSearch: {
      type: Boolean,
      default: false
    },
    // 搜索字段配置
    searchFields: {
      type: Array,
      default: () => []
    },
    // 是否显示选择框
    showSelection: {
      type: Boolean,
      default: false
    },
    // 是否显示操作列
    showActions: {
      type: Boolean,
      default: true
    },
    // 操作列宽度
    actionWidth: {
      type: [String, Number],
      default: 150
    },
    // 操作按钮配置
    actions: {
      type: Array,
      default: () => []
    },
    // 是否显示分页
    showPagination: {
      type: Boolean,
      default: true
    },
    // 分页配置
    pagination: {
      type: Object,
      default: () => ({
        page: 1,
        size: 10,
        total: 0
      })
    },
    // 分页大小选项
    pageSizes: {
      type: Array,
      default: () => [10, 20, 50, 100]
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['search', 'reset', 'selection-change', 'sort-change', 'size-change', 'current-change'],
  setup(props, { emit }) {
    const tableData = ref([])
    const searchForm = reactive({})
    
    // 初始化搜索表单
    const initSearchForm = () => {
      props.searchFields.forEach(field => {
        searchForm[field.prop] = ''
      })
    }
    
    // 搜索
    const handleSearch = () => {
      emit('search', { ...searchForm })
    }
    
    // 重置
    const handleReset = () => {
      Object.keys(searchForm).forEach(key => {
        searchForm[key] = ''
      })
      emit('reset')
    }
    
    // 选择变化
    const handleSelectionChange = (selection) => {
      emit('selection-change', selection)
    }
    
    // 排序变化
    const handleSortChange = (sort) => {
      emit('sort-change', sort)
    }
    
    // 分页大小变化
    const handleSizeChange = (size) => {
      emit('size-change', size)
    }
    
    // 当前页变化
    const handleCurrentChange = (page) => {
      emit('current-change', page)
    }
    
    // 监听数据变化
    watch(() => props.data, (newData) => {
      tableData.value = newData
    }, { immediate: true })
    
    // 初始化
    initSearchForm()
    
    return {
      tableData,
      searchForm,
      handleSearch,
      handleReset,
      handleSelectionChange,
      handleSortChange,
      handleSizeChange,
      handleCurrentChange
    }
  }
})
</script>

<style scoped>
.table-container {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.table-search {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.table-pagination {
  margin-top: 16px;
  text-align: right;
}
</style>
