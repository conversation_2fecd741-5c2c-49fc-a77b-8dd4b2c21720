import { config } from '@vue/test-utils'
import ElementPlus from 'element-plus'

// 全局配置
config.global.plugins = [ElementPlus]

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
global.localStorage = localStorageMock

// Mock window.location
delete window.location
window.location = {
  href: 'http://localhost:3001',
  origin: 'http://localhost:3001',
  pathname: '/',
  search: '',
  hash: ''
}

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
}
