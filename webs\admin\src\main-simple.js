import { createApp } from 'vue'

// 创建一个简单的测试组件
const SimpleApp = {
  template: `
    <div style="padding: 20px; font-family: Arial, sans-serif;">
      <h1>🍽️ 楠楠家厨后台管理系统</h1>
      <p>系统正在加载中...</p>
      <p>当前时间: {{ currentTime }}</p>
      <div style="margin-top: 20px;">
        <button @click="testClick" style="padding: 10px 20px; background: #409eff; color: white; border: none; border-radius: 4px; cursor: pointer;">
          测试按钮
        </button>
      </div>
      <div v-if="clicked" style="margin-top: 10px; color: green;">
        ✅ Vue.js 正常工作！
      </div>
    </div>
  `,
  data() {
    return {
      currentTime: new Date().toLocaleString(),
      clicked: false
    }
  },
  methods: {
    testClick() {
      this.clicked = true
      console.log('Vue.js 测试成功！')
    }
  },
  mounted() {
    console.log('楠楠家厨后台管理系统 - 简单版本加载成功！')
    // 每秒更新时间
    setInterval(() => {
      this.currentTime = new Date().toLocaleString()
    }, 1000)
  }
}

// 创建应用实例
const app = createApp(SimpleApp)

// 挂载应用
app.mount('#app')
