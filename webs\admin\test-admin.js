#!/usr/bin/env node

/**
 * 楠楠家厨后台管理系统测试脚本
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log("🍽️ 楠楠家厨后台管理系统 - 功能测试");
console.log("=".repeat(50));

// 测试文件结构
function testFileStructure() {
  console.log("\n📁 测试文件结构...");

  const requiredFiles = [
    "package.json",
    "vite.config.js",
    "src/main.js",
    "src/App.vue",
    "src/router/index.js",
    "src/stores/user.js",
    "src/utils/request.js",
    "src/components/CustomTable.vue",
    "src/views/login/index.vue",
    "src/views/dashboard/index.vue",
    "src/views/menu/list.vue",
    "src/views/menu/dishes.vue",
    "src/views/order/list.vue",
    "src/views/message/list.vue",
    "src/views/user/list.vue",
    "src/views/statistics/overview.vue"
  ];

  let passed = 0;
  let failed = 0;

  requiredFiles.forEach(file => {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${file}`);
      passed++;
    } else {
      console.log(`❌ ${file} - 文件不存在`);
      failed++;
    }
  });

  console.log(`\n📊 文件结构测试结果: ${passed} 通过, ${failed} 失败`);
  return failed === 0;
}

// 测试配置文件
function testConfiguration() {
  console.log("\n⚙️ 测试配置文件...");

  try {
    // 测试 package.json
    const packageJson = JSON.parse(fs.readFileSync("package.json", "utf8"));
    console.log(`✅ package.json - 项目名称: ${packageJson.name}`);
    console.log(`✅ package.json - 版本: ${packageJson.version}`);

    // 检查必要的依赖
    const requiredDeps = [
      "vue",
      "vue-router",
      "pinia",
      "element-plus",
      "axios"
    ];
    const missingDeps = requiredDeps.filter(
      dep => !packageJson.dependencies[dep]
    );

    if (missingDeps.length === 0) {
      console.log("✅ 所有必要依赖已安装");
    } else {
      console.log(`❌ 缺少依赖: ${missingDeps.join(", ")}`);
      return false;
    }

    // 测试 vite.config.js
    if (fs.existsSync("vite.config.js")) {
      console.log("✅ vite.config.js 配置文件存在");
    } else {
      console.log("❌ vite.config.js 配置文件不存在");
      return false;
    }

    return true;
  } catch (error) {
    console.log(`❌ 配置文件测试失败: ${error.message}`);
    return false;
  }
}

// 测试API接口文件
function testApiFiles() {
  console.log("\n🔌 测试API接口文件...");

  const apiFiles = [
    "src/api/auth.js",
    "src/api/menu.js",
    "src/api/order.js",
    "src/api/message.js",
    "src/api/user.js"
  ];

  let passed = 0;
  let failed = 0;

  apiFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file}`);
      passed++;
    } else {
      console.log(`❌ ${file} - 文件不存在`);
      failed++;
    }
  });

  console.log(`\n📊 API文件测试结果: ${passed} 通过, ${failed} 失败`);
  return failed === 0;
}

// 测试组件文件
function testComponents() {
  console.log("\n🧩 测试组件文件...");

  const componentFiles = [
    "src/components/CustomTable.vue",
    "src/layout/index.vue",
    "src/views/menu/components/MenuForm.vue",
    "src/views/menu/components/DishForm.vue",
    "src/views/menu/components/DishSelector.vue"
  ];

  let passed = 0;
  let failed = 0;

  componentFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file}`);
      passed++;
    } else {
      console.log(`❌ ${file} - 文件不存在`);
      failed++;
    }
  });

  console.log(`\n📊 组件文件测试结果: ${passed} 通过, ${failed} 失败`);
  return failed === 0;
}

// 测试样式文件
function testStyles() {
  console.log("\n🎨 测试样式文件...");

  if (fs.existsSync("src/styles/index.scss")) {
    console.log("✅ src/styles/index.scss");
    return true;
  } else {
    console.log("❌ src/styles/index.scss - 文件不存在");
    return false;
  }
}

// 测试Jest配置
function testJestConfig() {
  console.log("\n🧪 测试Jest配置...");

  const testFiles = ["jest.config.js", "babel.config.js", "tests/setup.js"];

  let passed = 0;
  let failed = 0;

  testFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file}`);
      passed++;
    } else {
      console.log(`❌ ${file} - 文件不存在`);
      failed++;
    }
  });

  console.log(`\n📊 Jest配置测试结果: ${passed} 通过, ${failed} 失败`);
  return failed === 0;
}

// 主测试函数
async function runTests() {
  console.log("开始测试...\n");

  const tests = [
    { name: "文件结构", fn: testFileStructure },
    { name: "配置文件", fn: testConfiguration },
    { name: "API接口文件", fn: testApiFiles },
    { name: "组件文件", fn: testComponents },
    { name: "样式文件", fn: testStyles },
    { name: "Jest配置", fn: testJestConfig }
  ];

  let totalPassed = 0;
  let totalFailed = 0;

  for (const test of tests) {
    const result = test.fn();
    if (result) {
      totalPassed++;
    } else {
      totalFailed++;
    }
  }

  console.log("\n" + "=".repeat(50));
  console.log("🎉 测试完成!");
  console.log(`✅ 通过: ${totalPassed}`);
  console.log(`❌ 失败: ${totalFailed}`);

  if (totalFailed === 0) {
    console.log("\n🚀 所有测试通过！后台管理系统已准备就绪！");
    console.log("\n📝 使用说明:");
    console.log("1. 启动开发服务器: npm run dev");
    console.log("2. 访问地址: http://localhost:3001");
    console.log("3. 测试账号: 13800138000 / 123456");
    console.log("4. 运行测试: npm test");
  } else {
    console.log("\n⚠️ 部分测试失败，请检查上述错误信息");
  }
}

// 运行测试
runTests().catch(console.error);
